"use client";

import type { UseFormReturn } from "react-hook-form";
import { Card } from "~/components/Card";
import { Label } from "~/components/ui/label";
import { useStore } from "~/hooks/store";
import { railSpinAmounts } from "~/lib/constants";
import { FormItemType } from "~/lib/enums/enums";
import {
  LandingDescription,
  landingDescriptions,
  SnowLandingType,
  snowLandingTypes,
  type SnowLandingZone,
  snowLandingZones,
  SnowRailSpinDirection,
  snowRailSpinDirections,
} from "~/lib/enums/snow";
import { type NewSnowSlopeRailTag } from "~/server/db/snowSchema";
import { api } from "~/trpc/react";
import { FormItem, type FormItemProps } from "../../FormItem";
import { ExecutionSelect } from "../enumSelectors/ExecutionSelect";
import { GrabSelect } from "../enumSelectors/GrabSelect";
import { landingDescriptionFormItem } from "../enumSelectors/LandingDescription";
import { ModifierSelect } from "../enumSelectors/ModifierSelect";
import { RailFeatureSelect } from "../enumSelectors/RailFeatureSelect";
import { RailTrickSelect } from "../enumSelectors/RailTrickSelect";
import type { SnowFormProps } from "../SnowFormContainer";

interface Props {
  isSnowboard: boolean;
  form: UseFormReturn<SnowFormProps>;
  tag?: NewSnowSlopeRailTag;
}

interface FormItemSlopeRailProps extends FormItemProps {
  name: keyof SnowFormProps;
}

export const FormSlopeRail = ({ form, isSnowboard, tag }: Props) => {
  const isJump = isSnowboard;

  const { data: railTrickOptions } = api.snowOptions.getRailTricks.useQuery({
    isSnowboard,
  });
  const { data: grabOptions } = api.snowOptions.getGrabs.useQuery({
    isSnowboard,
  });
  const { data: railModifierOptions } = api.snowOptions.getModifiers.useQuery();

  const { data: executionOptions } = api.snowOptions.getExecutions.useQuery({
    isJump: isJump,
  });
  const currentFrame = useStore((state) => state.currentFrame);
  const { setValue, getValues } = form;
  const landingType = form.watch("landingType");

  const { data: railFeatures } = api.snowOptions.getRailFeatures.useQuery();

  const baseItems: FormItemSlopeRailProps[] = [
    {
      name: "score",
      title: "Score",
      type: FormItemType.number,
      className: "grid grid-cols-2 col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "h",
        className: "bg-blue-k40 text-blue-20 text-xs",
        onClick: () => {
          if (!tag) return;
          setValue("score", currentFrame?.toString() ?? "");
        },
      },
    },
    {
      name: "railFeatureId",
      title: "Rail Feature",
      type: FormItemType.editSelect,
      required: true,
      className: "grid grid-cols-2 col-span-full pb-2.5",
      hotkey: {
        value: "q",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = railFeatures;
          if (!options?.length) return;

          const currentValue = getValues("railFeatureId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railFeatureId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <RailFeatureSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "progression",
      title: "Progression",
      type: FormItemType.radio,
      className: "flex items-center col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "w",
        className:
          "text-xs group-data-[state=checked]:bg-fuchsia-20 text-black data-[state=checked]:text-fuchsia-k40",

        onClick: () => {
          const currentValue = form.getValues("progression");
          setValue("progression", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },
    {
      name: "switch",
      title: "Orientation",
      type: FormItemType.radio,
      className: "flex items-center col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "e",
        className:
          "group-data-[state=checked]:bg-orange-20 text-black data-[state=checked]:text-orange-k40",
        onClick: () => {
          const currentValue = form.getValues("switch");
          setValue("switch", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Switch", value: "1" },
        { label: "Forwards", value: "0" },
      ],
    },
    {
      name: "cab",
      title: "Cab",
      type: FormItemType.radio,
      className: "flex items-center col-span-full pb-2.5",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "r",
        className:
          "group-data-[state=checked]:bg-orange-k40 text-black data-[state=checked]:text-orange-20",
        onClick: () => {
          const currentValue = form.getValues("cab");
          setValue("cab", currentValue === "1" ? "0" : "1");
        },
      },
      options: [
        { label: "Yes", value: "1" },
        { label: "No", value: "0" },
      ],
    },

    {
      name: "grabStart",
      title: "Grab Start Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "3",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("grabStart", currentFrame.toString());
        },
      },
    },
    {
      name: "grabEnd",
      title: "Grab End Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "4",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("grabEnd", currentFrame.toString());
        },
      },
    },
    {
      name: "takeOffFrame",
      title: "Take-off Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "5",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("takeOffFrame", currentFrame.toString());
        },
      },
    },
    {
      name: "landingFrame",
      title: "Landing Frame",
      className: "grid",
      type: FormItemType.number,
      hotkey: {
        value: "6",
        className: "text-xs",
        onClick: () => {
          if (!currentFrame) return;
          setValue("landingFrame", currentFrame.toString());
        },
      },
    },

    {
      name: "executionId",
      title: "Execution",
      type: FormItemType.editSelect,
      className: "grid col-span-full",
      hotkey: {
        value: "b",
        className: "bg-neonGreen-k40 text-neonGreen-20 text-xs",
        onClick: () => {
          const options = executionOptions;
          if (!options?.length) return;

          const currentValue = getValues("executionId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("executionId", options[nextIndex]?.value ?? "");

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <ExecutionSelect
          isJump={true}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "landingZone",
      title: "Landing Zone:",
      type: FormItemType.radio,
      className: "flex items-start col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "z",
        className:
          "group-data-[state=checked]:bg-neonGreen text-black/60 text-xs data-[state=checked]:text-black/60",
        onClick: () => {
          const currentValue = form.getValues("landingZone");
          const currentIndex = snowLandingZones.indexOf(
            currentValue as SnowLandingZone,
          );
          const nextIndex = (currentIndex + 1) % snowLandingZones.length;
          setValue("landingZone", snowLandingZones[nextIndex] ?? "");
        },
      },
      options: snowLandingZones.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "landingType",
      title: "Landing Type:",
      type: FormItemType.radio,
      className: "flex items-start col-span-full",
      labelClassName: "w-[98px]",
      hotkey: {
        value: "x",
        className: "group-data-[state=checked]:bg-blue text-black text-xs",
        onClick: () => {
          const currentValue = getValues("landingType");
          const currentIndex = snowLandingTypes.indexOf(
            currentValue as SnowLandingType,
          );
          const nextIndex = (currentIndex + 1) % snowLandingTypes.length;
          setValue("landingType", snowLandingTypes[nextIndex] ?? "");
        },
      },
      options: snowLandingTypes.map((x) => ({
        label: x,
        value: x,
      })),
    },
  ];

  const items =
    landingType === SnowLandingType.none
      ? baseItems
      : [
          ...baseItems,
          landingDescriptionFormItem({
            options: landingDescriptions
              .filter((x) => x !== LandingDescription["lost ski"])
              .map((x) => ({
                label: x,
                value: x,
              })),
          }),
        ];

  const inItems: FormItemSlopeRailProps[] = [
    {
      name: "railInSpinDirection",
      title: "Spin In Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      options: snowRailSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "t",
        className: "bg-red-20 text-red-k40 text-xs ",
        onClick: () => {
          const currentValue = getValues("railInSpinDirection")!;
          const currentIndex = snowRailSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowRailSpinDirections.length;
          setValue("railInSpinDirection", snowRailSpinDirections[nextIndex]);
        },
      },
    },
    {
      name: "railInSpinAmount",
      title: "Spin In Amount",

      type: FormItemType.select,
      placeholder: "Select Spin Amount",
      options: railSpinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "y",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railInSpinAmount",
          ) as SnowRailSpinDirection;
          const currentIndex = railSpinAmounts.indexOf(currentValue ?? "none");
          const nextIndex = (currentIndex + 1) % railSpinAmounts.length;
          setValue("railInSpinAmount", railSpinAmounts[nextIndex]);
        },
      },
    },
    {
      name: "railInSpinModifierId",
      title: "Spin In Modifier",
      type: FormItemType.editSelect,
      hotkey: {
        value: "u",
        className: "bg-blue-20 text-blue-k40 text-xs",
        onClick: () => {
          const options = railModifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("railInSpinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railInSpinModifierId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railInGrabId",
      title: "Grab Type",
      type: FormItemType.editSelect,
      hotkey: {
        value: "i",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("railInGrabId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railInGrabId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
  ];

  const onItems: FormItemSlopeRailProps[] = [
    {
      name: "railTrickId",
      title: "Rail Trick",
      required: true,
      className: "col-span-full",
      type: FormItemType.editSelect,
      hotkey: {
        value: "a",
        className: "bg-orange-20 text-orange-k40 text-xs",
        onClick: () => {
          const options = railTrickOptions;
          if (!options?.length) return;

          const currentValue = getValues("railTrickId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railTrickId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <RailTrickSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
    {
      name: "railOnSpinDirection",
      title: "Spin On Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      options: snowRailSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "s",
        className: "bg-red-20 text-red-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOnSpinDirection",
          ) as SnowRailSpinDirection;
          const currentIndex = snowRailSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowRailSpinDirections.length;
          setValue(
            "railOnSpinDirection",
            snowRailSpinDirections[nextIndex] ?? SnowRailSpinDirection.none,
          );
        },
      },
    },
    {
      name: "railOnSpinAmount",
      title: "Spin On Amount",
      type: FormItemType.select,
      placeholder: "Select Spin Amount",
      options: railSpinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
      hotkey: {
        value: "d",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOnSpinAmount",
          ) as SnowRailSpinDirection;
          const currentIndex = railSpinAmounts.indexOf(currentValue ?? "none");
          const nextIndex = (currentIndex + 1) % railSpinAmounts.length;
          setValue("railOnSpinAmount", railSpinAmounts[nextIndex]);
        },
      },
    },
    {
      name: "railOnSpinModifierId",
      title: "Spin On Modifier",
      type: FormItemType.editSelect,
      hotkey: {
        value: "f",
        className: "bg-blue-20 text-blue-k40 text-xs",
        onClick: () => {
          const options = railModifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOnSpinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOnSpinModifierId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railOnGrabId",
      title: "Grab On Type",
      type: FormItemType.editSelect,
      hotkey: {
        value: "g",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOnGrabId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOnGrabId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
  ];

  const offItems: FormItemSlopeRailProps[] = [
    {
      name: "railOutSpinDirection",
      title: "Spin Out Direction",
      type: FormItemType.select,
      placeholder: "Select Spin Direction",
      hotkey: {
        value: "p",
        className: "bg-red-20 text-red-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOutSpinDirection",
          ) as SnowRailSpinDirection;
          const currentIndex = snowRailSpinDirections.indexOf(
            currentValue ?? "none",
          );
          const nextIndex = (currentIndex + 1) % snowRailSpinDirections.length;
          setValue(
            "railOutSpinDirection",
            snowRailSpinDirections[nextIndex] ?? SnowRailSpinDirection.none,
          );
        },
      },
      options: snowRailSpinDirections.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "railOutSpinAmount",
      title: "Spin Out Amount",
      type: FormItemType.select,
      placeholder: "Select Spin Amount",
      hotkey: {
        value: "l",
        className: "bg-purple-20 text-purple-k40 text-xs",
        onClick: () => {
          const currentValue = getValues(
            "railOutSpinAmount",
          ) as SnowRailSpinDirection;
          const currentIndex = railSpinAmounts.indexOf(currentValue ?? "none");
          const nextIndex = (currentIndex + 1) % railSpinAmounts.length;
          setValue("railOutSpinAmount", railSpinAmounts[nextIndex]);
        },
      },
      options: railSpinAmounts.map((x) => ({
        label: x,
        value: x,
      })),
    },
    {
      name: "railOutSpinModifierId",
      title: "Spin Out Modifier",
      type: FormItemType.editSelect,
      hotkey: {
        value: "k",
        className: "bg-blue-20 text-blue-k40 text-xs",
        onClick: () => {
          const options = railModifierOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOutSpinModifierId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOutSpinModifierId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => <ModifierSelect value={field.value} onSelect={field.onChange} />,
    },
    {
      name: "railOutGrabId",
      title: "Spin Out Grab Type",
      type: FormItemType.editSelect,
      className: "",
      hotkey: {
        value: "m",
        className: "bg-yellow-20 text-yellow-k40 text-xs",
        onClick: () => {
          const options = grabOptions;
          if (!options?.length) return;

          const currentValue = getValues("railOutGrabId");
          const currentIndex = options.findIndex(
            (opt) => opt.value === currentValue,
          );
          const nextIndex = (currentIndex + 1) % options.length;
          setValue("railOutGrabId", options[nextIndex]?.value ?? null);

          if (!tag) return;
        },
      },
      CustomRender: (field: {
        value: string;
        onChange: (value: string) => void;
      }) => (
        <GrabSelect
          isSnowboard={isSnowboard}
          value={field.value}
          onSelect={field.onChange}
        />
      ),
    },
  ];

  const firstItems = items.slice(0, 5);

  const remainingItems = items.slice(-8);

  return (
    <Card className="grid grid-cols-2 gap-2.5">
      <div className="col-span-full">
        <Label className="!text-smallLabel uppercase text-black/60">
          RAIL DETAILS:
        </Label>
      </div>

      {/* Basic Details Section */}
      <div className="col-span-full mb-4">
        {firstItems.map((item) => (
          <FormItem key={item.name} control={form.control} {...item} />
        ))}
      </div>

      {/* Rail In Section */}
      <div className="col-span-full mb-4">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          SPIN IN:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {inItems.map((item) => (
            <FormItem control={form.control} key={item.name} {...item} />
          ))}
        </div>
      </div>

      {/* Rail On Section */}
      <div className="col-span-full mb-4">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          SPIN ON:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {onItems.map((item) => (
            <FormItem control={form.control} key={item.name} {...item} />
          ))}
        </div>
      </div>

      {/* Rail Off Section */}
      <div className="col-span-full mb-4">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          SPIN OUT:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {offItems.map((item) => (
            <FormItem control={form.control} key={item.name} {...item} />
          ))}
        </div>
      </div>

      {/* Landing Details Section */}
      <div className="col-span-full">
        <Label className="mb-2 !text-smallLabel uppercase text-black/60">
          LANDING DETAILS:
        </Label>
        <div className="grid grid-cols-2 gap-2">
          {remainingItems.map((item) => (
            <FormItem key={item.name} control={form.control} {...item} />
          ))}
        </div>
      </div>
    </Card>
  );
};
